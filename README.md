# Potree Converter & Viewer Docker Setup

This Docker setup provides a complete solution for processing LAS/LAZ point cloud files and viewing them through a dynamic web interface powered by Express.js and Potree.

## Features

- **Ubuntu 22.04** base image with all required dependencies
- **Volume mounting** for easy file input/output
- **Automatic conversion** of all LAS/LAZ files in the input folder
- **Dynamic web viewer** with Express.js server supporting both Potree 1.x and 2.x formats
- **Auto-detection** of point cloud formats and versions
- **RESTful API** for point cloud metadata and listing
- **Configurable conversion parameters** via environment variables
- **Nginx Proxy Manager** for easy SSL certificates and domain management
- **Public URLs** for sharing point clouds without authentication

## Directory Structure

```
potree/
├── Dockerfile              # Converter container definition
├── Dockerfile.viewer       # Viewer container definition
├── docker-compose.yml      # Service orchestration
├── convert.sh              # Conversion script
├── server.js               # Express.js web server
├── package.json            # Node.js dependencies
├── las_laz/                # Input folder (mount your LAS/LAZ files here)
│   └── P4_PCtest.las      # Example: Your point cloud files
├── output/                 # Output folder (converted Potree files)
│   ├── P4_PCtest/         # Example: Converted point cloud
│   │   ├── metadata.json  # Potree 2.x metadata
│   │   ├── hierarchy.bin  # Octree hierarchy
│   │   ├── octree.bin     # Point data
│   │   └── chunks/        # Additional data chunks
├── viewer/                 # Potree viewer library
│   ├── build/             # Potree build files
│   ├── libs/              # JavaScript libraries
│   └── examples/          # Example configurations
├── config/                 # Optional configuration files
├── nginx-proxy-manager/    # Nginx Proxy Manager data (optional)
│   ├── data/              # NPM configuration data
│   └── letsencrypt/       # SSL certificates
└── README.md               # This file
```

## Quick Start

1. **Create the input directory and add your files:**
   ```bash
   mkdir -p las_laz
   # Copy your .las or .laz files to the las_laz folder
   cp /path/to/your/pointcloud.las las_laz/
   ```

2. **Build and run the complete system:**
   ```bash
   # Start converter and web viewer
   docker-compose --profile viewer up -d --build

   # Optional: Start with Nginx Proxy Manager
   docker-compose --profile viewer --profile proxy up -d --build
   ```

3. **If you add files after starting the containers:**
   ```bash
   # Restart the converter to process new files
   docker restart potree-converter

   # Monitor conversion progress
   docker logs -f potree-converter
   ```

4. **Access the web interface:**
   - **Home page:** `http://localhost:8080/` - Lists all available point clouds (requires login: admin/1234)
   - **Public point cloud viewer:** `http://localhost:8080/public/{pointcloud_name}` - No authentication required
   - **Admin point cloud viewer:** `http://localhost:8080/{pointcloud_name}` - Requires authentication
   - **API endpoints:** `http://localhost:8080/api/pointclouds` - JSON list of point clouds
   - **Nginx Proxy Manager:** `http://localhost:81/` - SSL and domain management (<EMAIL>/changeme)

## Usage Options

### Option 1: Complete System (Recommended)
```bash
# Start converter and dynamic web viewer
docker-compose --profile viewer up -d --build

# Access the web interface:
# - Home: http://localhost:8080/ (admin/1234)
# - Public Point Cloud: http://localhost:8080/public/P2_Pointcloud/
# - Admin Point Cloud: http://localhost:8080/P2_Pointcloud/ (requires login)
# - API: http://localhost:8080/api/pointclouds
```

### Option 1b: Complete System with Nginx Proxy Manager
```bash
# Start converter, web viewer, and proxy manager
docker-compose --profile viewer --profile proxy up -d --build

# Access the interfaces:
# - Potree Home: http://localhost:8080/ (admin/1234)
# - Public Point Cloud: http://localhost:8080/public/P2_Pointcloud/
# - Nginx Proxy Manager: http://localhost:81/ (<EMAIL>/changeme)
```

### Option 2: Converter Only
```bash
# Build and run converter only
docker-compose up --build potree-converter
```

### Option 3: Interactive Mode
```bash
# Run in interactive mode
docker-compose run --rm potree-converter bash

# Inside the container, run manual conversions:
PotreeConverter /app/las_laz/P2_Pointcloud.las -o /app/output/P2_Pointcloud
```

## Configuration

You can customize the conversion process using environment variables in `docker-compose.yml`:

```yaml
environment:
  - CONVERT_ALL=true                    # Auto-convert all files
  - OUTPUT_FORMAT=potree               # Output format
  - SPACING=auto                       # Point spacing (auto or numeric value)
  - LEVELS=5                          # Number of LOD levels
  - OUTPUT_ATTRIBUTES=RGB,INTENSITY,CLASSIFICATION  # Attributes to include
```

## Manual Conversion Commands

If you prefer manual control, you can exec into the running container:

```bash
# Start container in background
docker-compose up -d

# Execute commands inside the container
docker exec -it potree-converter bash

# Run custom conversions
PotreeConverter /app/las_laz/input.las -o /app/output/custom_output --spacing 0.1 --levels 6
```

## Common PotreeConverter Options

- `--spacing <value>`: Point spacing for the output (e.g., 0.1, 0.01)
- `--levels <number>`: Number of levels in the octree (default: 5)
- `--output-attributes <list>`: Comma-separated list of attributes (RGB, INTENSITY, CLASSIFICATION, etc.)
- `--material <type>`: Material type (RGB, ELEVATION, INTENSITY, etc.)

## Troubleshooting

### Common Issues

1. **Files not converting after adding to las_laz folder**
   ```bash
   # The converter runs once at startup. If you add files after starting, restart the container:
   docker restart potree-converter

   # Monitor the conversion process:
   docker logs -f potree-converter
   ```

2. **No files found**: Make sure your LAS/LAZ files are in the `las_laz/` folder
   ```bash
   # Check if files are visible to the container:
   docker exec potree-converter ls -la /app/las_laz/
   ```

3. **Permission issues**: Ensure the Docker daemon has access to your directories
   ```bash
   # Fix permissions if needed:
   sudo chown -R $USER:$USER las_laz/ output/
   ```

4. **Memory issues**: Large point clouds may require more memory - adjust Docker settings
   ```bash
   # For large files, increase Docker memory limits in Docker Desktop settings
   # or add memory limits to docker-compose.yml:
   # deploy:
   #   resources:
   #     limits:
   #       memory: 4G
   ```

5. **Build failures**: Make sure you have sufficient disk space and internet connection

6. **Container keeps restarting**: Check container logs for errors
   ```bash
   docker logs potree-converter
   docker logs potree-viewer
   ```

7. **Web viewer not accessible**: Ensure port 8080 is not in use by another service
   ```bash
   # Check what's using port 8080:
   lsof -i :8080

   # Or change the port in docker-compose.yml:
   # ports:
   #   - "8081:3000"  # Use port 8081 instead
   ```

## File Formats Supported

- **Input**: LAS (.las), LAZ (.laz)
- **Output**: Potree format (octree structure with .bin files and metadata)

### Current Point Clouds Available

Your setup currently includes the following converted point clouds:
- P2_PC, P3_PC, P4_PC, P5_PC, P6_PC, P7_PC, P8_PC, P9_PC
- P10_PC, P11_PC, P12_PC, P13_PC, P14_PC, P15_PC, P16_PC, P17_PC, P18_PC, P19_PC
- P4_PCtest (recently converted from P4_PCtest.las)

Access any of these via:
- **Public URL**: `http://localhost:8080/public/{name}/` (e.g., `http://localhost:8080/public/P4_PCtest/`)
- **Admin URL**: `http://localhost:8080/{name}/` (requires login)

## Web Viewer Features

The dynamic web viewer is powered by Express.js and provides:

### **Home Page** (`http://localhost:8080/`)
- Lists all available point clouds with version badges
- Auto-detects Potree 1.x (cloud.js) and 2.x (metadata.json) formats
- Shows validation status for each point cloud
- Direct links to individual viewers

### **Point Cloud Viewer** (`http://localhost:8080/{name}/`)
- Interactive 3D Potree viewer
- Supports both Potree 1.x and 2.x formats
- Auto-detection of point cloud version
- Full navigation and visualization controls
- Material and rendering options

### **API Endpoints**
- `GET /api/pointclouds` - JSON list of all point clouds
- `GET /api/pointcloud/{name}` - Metadata for specific point cloud
- `GET /data/{name}/` - Static file serving for point cloud data

### **Supported Formats**
- **Potree 1.x**: Uses `cloud.js` files
- **Potree 2.x**: Uses `metadata.json` files with binary data
- Auto-detection based on file presence and metadata version

## Examples

### Converting and Viewing a Point Cloud
```bash
# 1. Add your LAS file
cp mydata.las las_laz/

# 2. Start the system
docker-compose --profile viewer up -d --build

# 3. If you added files after starting, restart the converter:
docker restart potree-converter

# 4. Wait for conversion to complete (check logs)
docker logs -f potree-converter

# 5. Access the viewer
# Home: http://localhost:8080/ (admin/1234)
# Public: http://localhost:8080/public/mydata/
# Admin: http://localhost:8080/mydata/ (requires login)
```

### API Usage
```bash
# List all point clouds
curl http://localhost:8080/api/pointclouds

# Get specific point cloud metadata
curl http://localhost:8080/api/pointcloud/P2_Pointcloud
```

## Stopping the Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v

# Stop and remove images
docker-compose down --rmi all
```

## Development

The Express.js server (`server.js`) provides:
- Dynamic routing for point clouds
- Auto-detection of Potree formats
- RESTful API for metadata
- Static file serving optimized for Potree
- Error handling and validation

To modify the viewer, edit `server.js` and restart the viewer container:
```bash
docker-compose restart potree-viewer
```

## Nginx Proxy Manager Setup

Nginx Proxy Manager provides an easy-to-use web interface for managing reverse proxies, SSL certificates, and domain routing.

### Initial Setup

1. **Start Nginx Proxy Manager:**
   ```bash
   docker-compose --profile proxy up -d
   ```

2. **Access the admin interface:**
   - URL: `http://localhost:81/`
   - Default credentials:
     - Email: `<EMAIL>`
     - Password: `changeme`

3. **Change default credentials immediately after first login!**

### Setting Up SSL for Your Domain

1. **Add a Proxy Host:**
   - Go to "Proxy Hosts" → "Add Proxy Host"
   - Domain Names: `your-domain.com`
   - Forward Hostname/IP: `potree-viewer` (container name)
   - Forward Port: `3000`

2. **Enable SSL:**
   - Go to "SSL" tab
   - Select "Request a new SSL Certificate"
   - Enable "Force SSL" and "HTTP/2 Support"
   - Add your email for Let's Encrypt notifications

3. **Advanced Configuration (Optional):**
   ```nginx
   # Custom Nginx configuration for large point cloud files
   client_max_body_size 1G;
   proxy_read_timeout 300;
   proxy_connect_timeout 300;
   proxy_send_timeout 300;
   ```

### URL Structure with Proxy Manager

After setting up your domain with SSL:

- **Public Access:** `https://your-domain.com/public/P2_Pointcloud`
- **Admin Access:** `https://your-domain.com/` (requires login)
- **API Access:** `https://your-domain.com/api/pointclouds`

### Benefits of Using Nginx Proxy Manager

- **Free SSL Certificates** from Let's Encrypt
- **Easy domain management** with web interface
- **Automatic certificate renewal**
- **Access control** and authentication
- **Load balancing** for multiple instances
- **Custom headers** and redirects
